<script setup lang="ts">
import { deleteMemoApi, getMemoApi } from '@/api/courseSchedule'
import AddMemoDialog from './AddMemoDialog.vue'

let props = defineProps({
  currentWeek: {
    type: Number,
    default: () => null,
  },
  currentApiParam: {
    type: Object,
    default: () => {},
  },
})
let showAdd = $ref<any>(false)
let currentMemo = $ref<any>(null)
let memoList = $ref<any>([])
const show = defineModel<boolean>('show')
function OnAddClick(data?) {
  showAdd = true
  currentMemo = data
}
async function getMemoList() {
  const res = await getMemoApi({
    weekIndex: props.currentWeek,
    ...props.currentApiParam,
  })
  memoList = res || []
}

async function onDelete({
  schoolTeacherMemoId,
  memoTitle,
}) {
  $g.confirm({
    content: `确定删除“${memoTitle}“吗？`,
  })
    .then(async () => {
      const res = await deleteMemo<PERSON><PERSON>({ schoolTeacherMemoId })
      getMemoList()
    })
    .catch(() => {})
}
watch(() => show.value, () => {
  if (show.value)
    getMemoList()

  else
    refreshTable()
})
const refreshTable = inject('refreshTable') as any
function refresh() {
  getMemoList()
}
</script>

<template>
  <div>
    <el-dialog
      v-model="show"
      class="w-[400px]  !px-0"
      align-center
      :show-close="false"
    >
      <div
        class="flex relative w-full text-[17px] font-500 mb-[14px] justify-center items-center"
      >
        备忘录<img
          class="w-[27px] cursor-pointer absolute right-[26px] h-[27px]"
          src="@/assets/img/courseSchedule/addMemoIcon.png"
          @click="OnAddClick()"
        />
      </div>
      <div class="px-[17px] h-[220px] overflow-y-auto ">
        <div
          v-for="(memoItem, memoIndex) in memoList"
          :key="memoIndex"
          class="border mb-[6px] cursor-pointer py-[8px] px-[13px] br-[8px] border-[#E0E0E0]"
          @click="OnAddClick(memoItem)"
        >
          <div class="flex items-center  justify-between ">
            <div class="text-[15px] truncate">
              {{ memoItem.memoTitle
              }}
            </div><img
              class="w-[13px] h-[13px] ml-[10px]"
              src="@/assets/img/courseSchedule/delete.png"
              @click.stop="onDelete(memoItem)"
            />
          </div>
          <div class="flex items-center justify-between">
            <div class="text-[9px] truncate">
              {{ memoItem.memoContent
              }}
            </div><div class="text-[11px] ml-[10px] flex-shrink-0">
              {{ $g.dayjs(memoItem.createTime).format('YYYY.MM.DD HH:mm') }}
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div>
          <el-button class="bg-[#999999]/[0.1048] mt-[10px] mr-[17px] w-[75px] h-[30px]" @click="show = false">
            取消
          </el-button>
        </div>
      </template>
    </el-dialog>
    <AddMemoDialog
      v-model:show="showAdd"
      :data="currentMemo"
      :current-api-param="currentApiParam"
      @refresh="refresh"
    ></AddMemoDialog>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dialog) {
  border-radius: 12px !important;
}
</style>
